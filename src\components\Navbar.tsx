'use client'

import { ThemeController, UserProfile } from '@/components'

export function Navbar() {
  return (
    <nav
      role="navigation"
      aria-label="Top navigation"
      className="flex items-center justify-between px-4 bg-[var(--layout-topbar-background)] z-30 h-16 rounded-box"
    >
      <div className="inline-flex items-center gap-1.5">
        <label
          htmlFor="sidebar-drawer"
          className="btn btn-square btn-ghost lg:hidden"
          aria-label="Toggle sidebar"
          tabIndex={0}
        >
          <span className="icon-[solar--hamburger-menu-line-duotone]"></span>
        </label>
        <button className="btn btn-outline btn-sm btn-ghost border-base-300 text-base-content/70 hidden h-9 w-48 justify-start gap-3 text-sm md:flex">
          <span className="icon-[solar--magnifer-outline] size-4"></span>
          <span>Search</span>
        </button>
      </div>
      <div className="inline-flex items-center gap-1.5">
        <ThemeController />
        <UserProfile />
      </div>
    </nav>
  )
}
