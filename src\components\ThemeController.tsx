'use client'

import { useEffect } from 'react'
import { useThemeStore, Theme } from '@/stores'

export function ThemeController() {
  const { theme, resolvedTheme, setTheme, initializeTheme } = useThemeStore()

  useEffect(() => {
    initializeTheme()
  }, [initializeTheme])

  const handleThemeSelect = (selectedTheme: Theme) => {
    setTheme(selectedTheme)
  }

  const getCurrentIcon = () => {
    if (theme === 'system') {
      const isDark = resolvedTheme === 'dark'
      return isDark
        ? 'icon-[solar--moon-stars-bold-duotone] text-violet-400'
        : 'icon-[solar--sun-fog-bold-duotone] text-amber-500'
    }
    if (theme === 'dark') return 'icon-[solar--moon-stars-bold-duotone] text-violet-400'
    if (theme === 'light') return 'icon-[solar--sun-fog-bold-duotone] text-amber-500'
    return 'icon-[solar--monitor-bold-duotone]'
  }

  return (
    <div className="dropdown dropdown-end">
      <div
        tabIndex={0}
        role="button"
        className="btn btn-circle btn-outline border-base-300"
        aria-label={`Current theme: ${theme}`}
        aria-haspopup="menu"
        aria-expanded="false"
      >
        <span className={getCurrentIcon()} aria-hidden="true"></span>
      </div>

      <ul
        tabIndex={0}
        className="dropdown-content menu gap-1 bg-base-100 rounded-box z-[1] w-40 shadow-md border border-base-300"
        role="menu"
        aria-label="Theme selection menu"
      >
        <li role="none">
          <button
            type="button"
            className={`flex items-center gap-2 px-3 py-2 hover:bg-base-200 ${
              theme === 'light' ? 'bg-primary text-primary-content hover:bg-primary' : ''
            }`}
            onClick={() => handleThemeSelect('light')}
            role="menuitem"
            aria-label="Light"
          >
            <span className="icon-[solar--sun-fog-bold-duotone]" aria-hidden="true"></span>
            <div className="flex flex-col items-start">
              <span className="font-medium">Light</span>
            </div>
            {theme === 'light' && (
              <span className="icon-[solar--check-circle-bold] ml-auto" aria-hidden="true"></span>
            )}
          </button>
        </li>
        <li role="none">
          <button
            type="button"
            className={`flex items-center gap-2 px-3 py-2 hover:bg-base-200 ${
              theme === 'dark' ? 'bg-primary text-primary-content hover:bg-primary' : ''
            }`}
            onClick={() => handleThemeSelect('dark')}
            role="menuitem"
            aria-label="Dark"
          >
            <span className="icon-[solar--moon-stars-bold-duotone]" aria-hidden="true"></span>
            <div className="flex flex-col items-start">
              <span className="font-medium">Dark</span>
            </div>
            {theme === 'dark' && (
              <span className="icon-[solar--check-circle-bold] ml-auto" aria-hidden="true"></span>
            )}
          </button>
        </li>
        <li role="none">
          <button
            type="button"
            className={`flex items-center gap-2 px-3 py-2 hover:bg-base-200 ${
              theme === 'system' ? 'bg-primary text-primary-content hover:bg-primary' : ''
            }`}
            onClick={() => handleThemeSelect('system')}
            role="menuitem"
            aria-label="System"
          >
            <span className="icon-[solar--monitor-bold-duotone]" aria-hidden="true"></span>
            <div className="flex flex-col items-start">
              <span className="font-medium">System</span>
            </div>
            {theme === 'system' && (
              <span className="icon-[solar--check-circle-bold] ml-auto" aria-hidden="true"></span>
            )}
          </button>
        </li>
      </ul>
    </div>
  )
}
