import Image from 'next/image'
import Link from 'next/link'
import { ThemeController } from '@/components/ThemeController'

export default function ForgotPasswordPage() {
  return (
    <div className="min-h-screen flex items-center justify-center">
      <div className="absolute inset-0 bg-gradient-to-b from-primary/20 dark:from-primary/10 dark:via-transparent to-secondary/20 dark:to-secondary/10"></div>
      <div className="relative w-full max-w-md p-8 sm:p-10 lg:p-12 space-y-10">
        <div className="flex items-center justify-between relative">
          <Link href="/">
            <Image src="/logo-light.svg" alt="logo" width={103} height={20} />
          </Link>
          <ThemeController />
        </div>
        <div className="text-center space-y-2">
          <h3 className="text-3xl font-semibold">Forgot something?</h3>
          <p className="text-sm text-base-content/70">
            Enter account email so we can reset your password
          </p>
        </div>
        <div className="space-y-4">
          <fieldset className="fieldset">
            <legend className="fieldset-legend text-sm">Email Address</legend>
            <label className="input w-full focus:outline-0">
              <span className="icon-[solar--mailbox-bold-duotone]"></span>
              <input type="email" placeholder="Email Address" className="grow focus:outline-0" />
            </label>
          </fieldset>
          <div className="flex items-center gap-3">
            <input
              type="checkbox"
              id="agreement"
              className="checkbox checkbox-sm checkbox-primary"
              aria-label="Terms agreement"
            />
            <label htmlFor="agreement" className="text-sm">
              I agree with
              <span className="text-primary cursor-pointer ms-1 hover:underline">
                terms and conditions
              </span>
            </label>
          </div>
          <button className="btn btn-primary w-full gap-3">
            <span className="icon-[solar--inbox-in-bold-duotone]"></span>
            Send reset link
          </button>
        </div>
      </div>
    </div>
  )
}
