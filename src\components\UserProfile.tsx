'use client'

import Image from 'next/image'
import Link from 'next/link'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/Dropdown'

interface UserData {
  name: string
  role: string
  avatar: string
}

export function UserProfile({
  user = {
    name: 'Username',
    role: 'Role',
    avatar: '/1.png',
  },
}: {
  user?: UserData
}) {
  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <button className="btn btn-ghost p-2" aria-label={`User menu for ${user.name}`}>
          <div className="flex items-center gap-2">
            <div className="avatar">
              <div className="bg-base-200 mask mask-circle w-8 relative">
                <Image src={user.avatar} alt={`${user.name}'s avatar`} width={32} height={32} />
              </div>
            </div>
            <div className="flex flex-col text-start">
              <span className="text-sm font-semibold truncate">{user.name}</span>
              <span className="text-xs text-base-content/50 truncate">{user.role}</span>
            </div>
          </div>
        </button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" sideOffset={16} aria-label="User menu">
        <DropdownMenuItem asChild>
          <Link href="/user/profile">
            <span className="icon-[solar--user-rounded-bold-duotone]" aria-hidden="true" />
            <span>My Account</span>
          </Link>
        </DropdownMenuItem>
        <DropdownMenuItem asChild>
          <Link href="/user/settings">
            <span className="icon-[solar--settings-bold-duotone]" aria-hidden="true" />
            <span>Settings</span>
          </Link>
        </DropdownMenuItem>
        <DropdownMenuItem asChild>
          <Link href="/help">
            <span className="icon-[solar--question-circle-bold-duotone]" aria-hidden="true" />
            <span>Help & Support</span>
          </Link>
        </DropdownMenuItem>
        <DropdownMenuSeparator />
        <DropdownMenuItem variant="destructive">
          <span className="icon-[solar--logout-bold-duotone]" aria-hidden="true" />
          <span>Log out</span>
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  )
}
